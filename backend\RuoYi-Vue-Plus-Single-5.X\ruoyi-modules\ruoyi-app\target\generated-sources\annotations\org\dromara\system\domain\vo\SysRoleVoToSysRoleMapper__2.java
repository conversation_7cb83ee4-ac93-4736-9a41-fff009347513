package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysRoleToSysRoleVoMapper__2.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__2 extends BaseMapper<SysRoleVo, SysRole> {
}
