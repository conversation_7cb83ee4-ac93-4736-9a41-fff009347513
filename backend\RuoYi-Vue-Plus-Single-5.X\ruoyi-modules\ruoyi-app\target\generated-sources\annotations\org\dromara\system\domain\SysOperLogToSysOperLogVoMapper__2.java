package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__2;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysOperLogBoToSysOperLogMapper__2.class,SysOperLogVoToSysOperLogMapper__2.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__2 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
