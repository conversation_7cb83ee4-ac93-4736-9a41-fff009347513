package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper__2;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {KnowledgeDocumentBoToKnowledgeDocumentMapper__2.class,KnowledgeDocumentVoToKnowledgeDocumentMapper__2.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper__2 extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}
