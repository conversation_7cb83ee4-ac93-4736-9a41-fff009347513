package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper__2;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {PaymentOrderBoToSysPaymentOrderMapper__2.class,PaymentOrderVoToSysPaymentOrderMapper__2.class},
    imports = {}
)
public interface SysPaymentOrderToPaymentOrderVoMapper__2 extends BaseMapper<SysPaymentOrder, PaymentOrderVo> {
}
