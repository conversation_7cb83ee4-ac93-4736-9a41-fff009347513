package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper__2;
import org.dromara.system.domain.vo.MajorVo;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {MajorVoToSysMajorMapper__2.class,MajorBoToSysMajorMapper__2.class},
    imports = {}
)
public interface SysMajorToMajorVoMapper__2 extends BaseMapper<SysMajor, MajorVo> {
}
