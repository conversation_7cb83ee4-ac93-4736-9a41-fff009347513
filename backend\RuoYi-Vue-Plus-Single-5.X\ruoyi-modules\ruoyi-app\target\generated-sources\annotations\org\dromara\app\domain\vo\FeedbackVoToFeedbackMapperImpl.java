package org.dromara.app.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.Feedback;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:13:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class FeedbackVoToFeedbackMapperImpl implements FeedbackVoToFeedbackMapper {

    @Override
    public Feedback convert(FeedbackVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Feedback feedback = new Feedback();

        feedback.setCreateTime( arg0.getCreateTime() );
        feedback.setUpdateTime( arg0.getUpdateTime() );
        feedback.setId( arg0.getId() );
        feedback.setUserId( arg0.getUserId() );
        feedback.setType( arg0.getType() );
        feedback.setContent( arg0.getContent() );
        feedback.setContactInfo( arg0.getContactInfo() );
        feedback.setDeviceInfo( arg0.getDeviceInfo() );
        feedback.setAppVersion( arg0.getAppVersion() );
        feedback.setPlatform( arg0.getPlatform() );
        feedback.setStatus( arg0.getStatus() );
        feedback.setReply( arg0.getReply() );
        feedback.setHandler( arg0.getHandler() );

        return feedback;
    }

    @Override
    public Feedback convert(FeedbackVo arg0, Feedback arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setContent( arg0.getContent() );
        arg1.setContactInfo( arg0.getContactInfo() );
        arg1.setDeviceInfo( arg0.getDeviceInfo() );
        arg1.setAppVersion( arg0.getAppVersion() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setReply( arg0.getReply() );
        arg1.setHandler( arg0.getHandler() );

        return arg1;
    }
}
