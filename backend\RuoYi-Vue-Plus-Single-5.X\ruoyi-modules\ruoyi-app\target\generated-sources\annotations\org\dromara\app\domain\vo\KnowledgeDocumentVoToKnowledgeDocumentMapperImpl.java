package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeDocument;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:13:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class KnowledgeDocumentVoToKnowledgeDocumentMapperImpl implements KnowledgeDocumentVoToKnowledgeDocumentMapper {

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocument knowledgeDocument = new KnowledgeDocument();

        knowledgeDocument.setCreateDept( arg0.getCreateDept() );
        knowledgeDocument.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeDocument.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeDocument.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeDocument.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeDocument.setId( arg0.getId() );
        knowledgeDocument.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocument.setTitle( arg0.getTitle() );
        knowledgeDocument.setContent( arg0.getContent() );
        knowledgeDocument.setDocType( arg0.getDocType() );
        knowledgeDocument.setSource( arg0.getSource() );
        knowledgeDocument.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocument.setFilePath( arg0.getFilePath() );
        knowledgeDocument.setFileSize( arg0.getFileSize() );
        knowledgeDocument.setStatus( arg0.getStatus() );
        knowledgeDocument.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocument.setVectorCount( arg0.getVectorCount() );
        knowledgeDocument.setSummary( arg0.getSummary() );
        knowledgeDocument.setTags( arg0.getTags() );
        knowledgeDocument.setMetadata( arg0.getMetadata() );
        knowledgeDocument.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocument.setErrorMessage( arg0.getErrorMessage() );
        knowledgeDocument.setLastProcessTime( arg0.getLastProcessTime() );
        knowledgeDocument.setSortOrder( arg0.getSortOrder() );
        knowledgeDocument.setRemark( arg0.getRemark() );

        return knowledgeDocument;
    }

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentVo arg0, KnowledgeDocument arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setContent( arg0.getContent() );
        arg1.setDocType( arg0.getDocType() );
        arg1.setSource( arg0.getSource() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setErrorMessage( arg0.getErrorMessage() );
        arg1.setLastProcessTime( arg0.getLastProcessTime() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
