package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysNoticeToSysNoticeVoMapper__2.class},
    imports = {}
)
public interface SysNoticeVoToSysNoticeMapper__2 extends BaseMapper<SysNoticeVo, SysNotice> {
}
