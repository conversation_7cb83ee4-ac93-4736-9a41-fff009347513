package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__2;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysOssConfigVoToSysOssConfigMapper__2.class,SysOssConfigBoToSysOssConfigMapper__2.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__2 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
