package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.AppUserManageBoToAppUserMapper__2;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.app.domain.vo.AppUserManageVoToAppUserMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {AppUserManageBoToAppUserMapper__2.class,AppUserManageVoToAppUserMapper__2.class},
    imports = {}
)
public interface AppUserToAppUserManageVoMapper__2 extends BaseMapper<AppUser, AppUserManageVo> {
}
