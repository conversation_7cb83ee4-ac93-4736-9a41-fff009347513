package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysDictDataToSysDictDataVoMapper__2.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__2 extends BaseMapper<SysDictDataVo, SysDictData> {
}
