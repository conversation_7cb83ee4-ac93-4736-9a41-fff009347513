package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysPostToSysPostVoMapper__2.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__2 extends BaseMapper<SysPostVo, SysPost> {
}
