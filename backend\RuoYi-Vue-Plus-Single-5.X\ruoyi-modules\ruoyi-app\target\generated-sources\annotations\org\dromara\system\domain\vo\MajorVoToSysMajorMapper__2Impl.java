package org.dromara.system.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysMajor;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T13:05:31+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MajorVoToSysMajorMapper__2Impl implements MajorVoToSysMajorMapper__2 {

    @Override
    public SysMajor convert(MajorVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMajor sysMajor = new SysMajor();

        if ( arg0.getCreateBy() != null ) {
            sysMajor.setCreateBy( Long.parseLong( arg0.getCreateBy() ) );
        }
        if ( arg0.getCreateTime() != null ) {
            sysMajor.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateBy() != null ) {
            sysMajor.setUpdateBy( Long.parseLong( arg0.getUpdateBy() ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            sysMajor.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        sysMajor.setColor( arg0.getColor() );
        sysMajor.setIcon( arg0.getIcon() );
        sysMajor.setMajorCode( arg0.getMajorCode() );
        sysMajor.setMajorId( arg0.getMajorId() );
        sysMajor.setMajorName( arg0.getMajorName() );
        sysMajor.setQuestionBankCount( arg0.getQuestionBankCount() );
        sysMajor.setRemark( arg0.getRemark() );
        sysMajor.setSort( arg0.getSort() );
        sysMajor.setStatus( arg0.getStatus() );

        return sysMajor;
    }

    @Override
    public SysMajor convert(MajorVo arg0, SysMajor arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateBy() != null ) {
            arg1.setCreateBy( Long.parseLong( arg0.getCreateBy() ) );
        }
        else {
            arg1.setCreateBy( null );
        }
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateBy() != null ) {
            arg1.setUpdateBy( Long.parseLong( arg0.getUpdateBy() ) );
        }
        else {
            arg1.setUpdateBy( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setColor( arg0.getColor() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setMajorCode( arg0.getMajorCode() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setMajorName( arg0.getMajorName() );
        arg1.setQuestionBankCount( arg0.getQuestionBankCount() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
