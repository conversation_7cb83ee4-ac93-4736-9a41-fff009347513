package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper__2.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper__2 extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}
