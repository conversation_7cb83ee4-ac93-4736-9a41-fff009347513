package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {},
    imports = {}
)
public interface PaymentOrderBoToSysPaymentOrderMapper__2 extends BaseMapper<PaymentOrderBo, SysPaymentOrder> {
}
