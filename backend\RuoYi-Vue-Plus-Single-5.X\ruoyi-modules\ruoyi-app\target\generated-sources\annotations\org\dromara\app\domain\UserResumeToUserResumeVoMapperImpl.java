package org.dromara.app.domain;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.UserResumeVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:13:35+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class UserResumeToUserResumeVoMapperImpl implements UserResumeToUserResumeVoMapper {

    @Override
    public UserResumeVo convert(UserResume arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserResumeVo userResumeVo = new UserResumeVo();

        userResumeVo.setResumeId( arg0.getResumeId() );
        userResumeVo.setUserId( arg0.getUserId() );
        userResumeVo.setResumeName( arg0.getResumeName() );
        userResumeVo.setOriginalName( arg0.getOriginalName() );
        userResumeVo.setFilePath( arg0.getFilePath() );
        userResumeVo.setFileUrl( arg0.getFileUrl() );
        userResumeVo.setFileSize( arg0.getFileSize() );
        userResumeVo.setFileSuffix( arg0.getFileSuffix() );
        userResumeVo.setIsDefault( arg0.getIsDefault() );
        userResumeVo.setStatus( arg0.getStatus() );
        userResumeVo.setOssId( arg0.getOssId() );
        userResumeVo.setRemark( arg0.getRemark() );
        userResumeVo.setCreateTime( arg0.getCreateTime() );
        userResumeVo.setCreateBy( arg0.getCreateBy() );
        userResumeVo.setUpdateTime( arg0.getUpdateTime() );

        return userResumeVo;
    }

    @Override
    public UserResumeVo convert(UserResume arg0, UserResumeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setResumeId( arg0.getResumeId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setResumeName( arg0.getResumeName() );
        arg1.setOriginalName( arg0.getOriginalName() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileUrl( arg0.getFileUrl() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setFileSuffix( arg0.getFileSuffix() );
        arg1.setIsDefault( arg0.getIsDefault() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setOssId( arg0.getOssId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
