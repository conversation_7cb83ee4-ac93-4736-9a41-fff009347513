package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__89;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.KnowledgeDocumentToKnowledgeDocumentVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__89.class,
    uses = {KnowledgeDocumentToKnowledgeDocumentVoMapper.class},
    imports = {}
)
public interface KnowledgeDocumentVoToKnowledgeDocumentMapper extends BaseMapper<KnowledgeDocumentVo, KnowledgeDocument> {
}
