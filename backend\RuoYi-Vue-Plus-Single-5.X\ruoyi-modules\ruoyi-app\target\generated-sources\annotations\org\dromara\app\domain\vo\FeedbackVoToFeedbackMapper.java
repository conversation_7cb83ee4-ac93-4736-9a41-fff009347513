package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__89;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__89.class,
    uses = {FeedbackToFeedbackVoMapper.class},
    imports = {}
)
public interface FeedbackVoToFeedbackMapper extends BaseMapper<FeedbackVo, Feedback> {
}
