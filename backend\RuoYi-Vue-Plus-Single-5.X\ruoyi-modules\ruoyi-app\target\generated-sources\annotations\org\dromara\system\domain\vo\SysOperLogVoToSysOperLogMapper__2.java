package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysOperLogToSysOperLogVoMapper__2.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__2 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
