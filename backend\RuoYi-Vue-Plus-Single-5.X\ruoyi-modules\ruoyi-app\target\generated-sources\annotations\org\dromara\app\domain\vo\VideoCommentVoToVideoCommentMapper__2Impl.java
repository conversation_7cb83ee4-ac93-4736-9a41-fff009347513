package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.VideoComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T13:05:32+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class VideoCommentVoToVideoCommentMapper__2Impl implements VideoCommentVoToVideoCommentMapper__2 {

    @Override
    public VideoComment convert(VideoCommentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoComment videoComment = new VideoComment();

        if ( arg0.getCreateTime() != null ) {
            videoComment.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        videoComment.setContent( arg0.getContent() );
        videoComment.setId( arg0.getId() );
        videoComment.setLikeCount( arg0.getLikeCount() );
        videoComment.setParentId( arg0.getParentId() );
        videoComment.setUserId( arg0.getUserId() );
        videoComment.setVideoId( arg0.getVideoId() );

        return videoComment;
    }

    @Override
    public VideoComment convert(VideoCommentVo arg0, VideoComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setContent( arg0.getContent() );
        arg1.setId( arg0.getId() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setVideoId( arg0.getVideoId() );

        return arg1;
    }
}
