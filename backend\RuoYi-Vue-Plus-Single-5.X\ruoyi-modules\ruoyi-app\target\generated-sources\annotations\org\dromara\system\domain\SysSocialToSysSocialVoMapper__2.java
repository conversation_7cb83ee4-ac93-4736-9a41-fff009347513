package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__2;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysSocialBoToSysSocialMapper__2.class,SysSocialVoToSysSocialMapper__2.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__2 extends BaseMapper<SysSocial, SysSocialVo> {
}
