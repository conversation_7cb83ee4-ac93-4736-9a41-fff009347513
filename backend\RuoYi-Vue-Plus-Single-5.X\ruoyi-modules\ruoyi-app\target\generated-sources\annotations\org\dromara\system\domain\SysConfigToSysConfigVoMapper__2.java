package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__2;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysConfigVoToSysConfigMapper__2.class,SysConfigBoToSysConfigMapper__2.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__2 extends BaseMapper<SysConfig, SysConfigVo> {
}
