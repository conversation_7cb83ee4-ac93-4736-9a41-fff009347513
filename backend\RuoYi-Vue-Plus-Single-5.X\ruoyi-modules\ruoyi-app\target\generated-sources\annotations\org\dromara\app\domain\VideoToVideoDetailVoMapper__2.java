package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper__2;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {VideoMappingUtils.class,VideoDetailVoToVideoMapper__2.class},
    imports = {}
)
public interface VideoToVideoDetailVoMapper__2 extends BaseMapper<Video, VideoDetailVo> {
}
