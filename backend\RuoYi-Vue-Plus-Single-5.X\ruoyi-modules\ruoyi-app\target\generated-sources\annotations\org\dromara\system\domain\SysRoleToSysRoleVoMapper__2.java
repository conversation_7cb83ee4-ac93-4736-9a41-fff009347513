package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__2;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysRoleVoToSysRoleMapper__2.class,SysRoleBoToSysRoleMapper__2.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__2 extends BaseMapper<SysRole, SysRoleVo> {
}
