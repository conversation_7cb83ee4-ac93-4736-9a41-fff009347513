package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {},
    imports = {}
)
public interface InterviewResultBoToInterviewResultMapper__2 extends BaseMapper<InterviewResultBo, InterviewResult> {
}
