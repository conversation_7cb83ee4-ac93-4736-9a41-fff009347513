package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.InterviewModeVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T14:13:34+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class InterviewModeToInterviewModeVoMapperImpl implements InterviewModeToInterviewModeVoMapper {

    @Override
    public InterviewModeVo convert(InterviewMode arg0) {
        if ( arg0 == null ) {
            return null;
        }

        InterviewModeVo interviewModeVo = new InterviewModeVo();

        interviewModeVo.setId( arg0.getId() );
        interviewModeVo.setName( arg0.getName() );
        interviewModeVo.setDescription( arg0.getDescription() );
        interviewModeVo.setIcon( arg0.getIcon() );
        interviewModeVo.setColor( arg0.getColor() );
        interviewModeVo.setDuration( arg0.getDuration() );
        interviewModeVo.setDifficulty( arg0.getDifficulty() );
        List<String> list = arg0.getFeatures();
        if ( list != null ) {
            interviewModeVo.setFeatures( new ArrayList<String>( list ) );
        }
        interviewModeVo.setSortOrder( arg0.getSortOrder() );
        interviewModeVo.setStatus( arg0.getStatus() );
        if ( arg0.getCreateTime() != null ) {
            interviewModeVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            interviewModeVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return interviewModeVo;
    }

    @Override
    public InterviewModeVo convert(InterviewMode arg0, InterviewModeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setDifficulty( arg0.getDifficulty() );
        if ( arg1.getFeatures() != null ) {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.getFeatures().clear();
                arg1.getFeatures().addAll( list );
            }
            else {
                arg1.setFeatures( null );
            }
        }
        else {
            List<String> list = arg0.getFeatures();
            if ( list != null ) {
                arg1.setFeatures( new ArrayList<String>( list ) );
            }
        }
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}
