package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {},
    imports = {}
)
public interface KnowledgeDocumentBoToKnowledgeDocumentMapper__2 extends BaseMapper<KnowledgeDocumentBo, KnowledgeDocument> {
}
