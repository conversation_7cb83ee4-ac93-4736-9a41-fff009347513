package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__86;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__86.class,
    uses = {SysConfigToSysConfigVoMapper__2.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__2 extends BaseMapper<SysConfigVo, SysConfig> {
}
